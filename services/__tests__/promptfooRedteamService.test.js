import axios from "axios";
import {
  createRedteamConfig,
  startRedteamJob,
  getRedteamResults,
  getRedteamPlugins,
  getRedteamStrategies,
  cancelRedteamJob,
  getRedteamReport,
} from "../promptfooRedteamService";

// Mock axios
jest.mock("axios");

describe("PromptFoo Red Team Service", () => {
  const mockApiToken = "test-token";
  const PROMPTFOO_API_URL =
    process.env.PROMPTFOO_API_URL || "https://api.promptfoo.app";

  beforeEach(() => {
    // Set up environment variable
    process.env.PROMPTFOO_API_TOKEN = mockApiToken;
    // Clear mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up environment
    delete process.env.PROMPTFOO_API_TOKEN;
  });

  describe("createRedteamConfig", () => {
    it("should create a new red team config with valid data", async () => {
      const mockConfig = {
        purpose: "Test purpose",
        plugins: ["harmful:hate", "competitors"],
        strategies: ["jailbreak"],
      };
      const mockResponse = { data: { id: "123", ...mockConfig } };
      axios.post.mockResolvedValueOnce(mockResponse);

      const result = await createRedteamConfig(mockConfig);

      expect(axios.post).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/redteam/configs`,
        mockConfig,
        expect.objectContaining({
          headers: { Authorization: `Bearer ${mockApiToken}` },
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    it("should throw error when purpose is missing", async () => {
      const invalidConfig = {
        plugins: ["harmful:hate"],
      };

      await expect(createRedteamConfig(invalidConfig)).rejects.toThrow(
        "Config must include a purpose description"
      );
    });
  });

  describe("startRedteamJob", () => {
    it("should start a new red team job with valid config", async () => {
      const mockJobConfig = {
        target: {
          id: "https",
          config: {
            url: "https://example.com/api",
            method: "POST",
          },
        },
        plugins: ["harmful:hate", { id: "competitors", numTests: 10 }],
      };
      const mockResponse = { data: { jobId: "job123", status: "running" } };
      axios.post.mockResolvedValueOnce(mockResponse);

      const result = await startRedteamJob(mockJobConfig);

      expect(axios.post).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/jobs`,
        expect.objectContaining({
          target: mockJobConfig.target,
          plugins: expect.arrayContaining([
            { id: "harmful:hate" },
            { id: "competitors", numTests: 10 },
          ]),
        }),
        expect.objectContaining({
          headers: { Authorization: `Bearer ${mockApiToken}` },
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    it("should throw error when target is missing", async () => {
      const invalidConfig = {
        plugins: ["harmful:hate"],
      };

      await expect(startRedteamJob(invalidConfig)).rejects.toThrow(
        "Job config must include a target configuration"
      );
    });

    it("should convert string plugins to object format", async () => {
      const mockJobConfig = {
        target: { id: "https" },
        plugins: ["plugin1", "plugin2"],
      };
      const mockResponse = { data: { jobId: "job123" } };
      axios.post.mockResolvedValueOnce(mockResponse);

      await startRedteamJob(mockJobConfig);

      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          plugins: [{ id: "plugin1" }, { id: "plugin2" }],
        }),
        expect.any(Object)
      );
    });
  });

  describe("getRedteamResults", () => {
    it("should fetch red team results without params", async () => {
      const mockResponse = { data: { results: [] } };
      axios.get.mockResolvedValueOnce(mockResponse);

      const result = await getRedteamResults();

      expect(axios.get).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/results`,
        expect.objectContaining({
          headers: { Authorization: `Bearer ${mockApiToken}` },
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    it("should fetch results with pagination params", async () => {
      const params = { limit: 10, offset: 20 };
      const mockResponse = { data: { results: [] } };
      axios.get.mockResolvedValueOnce(mockResponse);

      const result = await getRedteamResults(params);

      expect(axios.get).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/results?limit=10&offset=20`,
        expect.any(Object)
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe("getRedteamPlugins", () => {
    it("should fetch all plugins when no category specified", async () => {
      const mockResponse = { data: { plugins: [] } };
      axios.get.mockResolvedValueOnce(mockResponse);

      const result = await getRedteamPlugins();

      expect(axios.get).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/redteam/plugins`,
        expect.any(Object)
      );
      expect(result).toEqual(mockResponse.data);
    });

    it("should fetch plugins filtered by category", async () => {
      const category = "security";
      const mockResponse = { data: { plugins: [] } };
      axios.get.mockResolvedValueOnce(mockResponse);

      const result = await getRedteamPlugins(category);

      expect(axios.get).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/redteam/plugins?category=${category}`,
        expect.any(Object)
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe("getRedteamStrategies", () => {
    it("should fetch available strategies", async () => {
      const mockResponse = {
        data: { strategies: ["jailbreak", "prompt-injection"] },
      };
      axios.get.mockResolvedValueOnce(mockResponse);

      const result = await getRedteamStrategies();

      expect(axios.get).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/redteam/strategies`,
        expect.any(Object)
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe("cancelRedteamJob", () => {
    it("should cancel a job successfully", async () => {
      const jobId = "job123";
      const mockResponse = { data: { status: "cancelled" } };
      axios.post.mockResolvedValueOnce(mockResponse);

      const result = await cancelRedteamJob(jobId);

      expect(axios.post).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/jobs/${jobId}/cancel`,
        {},
        expect.any(Object)
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe("getRedteamReport", () => {
    it("should fetch job report successfully", async () => {
      const jobId = "job123";
      const mockResponse = {
        data: {
          summary: { total: 10, passed: 8, failed: 2 },
          vulnerabilities: [],
        },
      };
      axios.get.mockResolvedValueOnce(mockResponse);

      const result = await getRedteamReport(jobId);

      expect(axios.get).toHaveBeenCalledWith(
        `${PROMPTFOO_API_URL}/api/v1/jobs/${jobId}/report`,
        expect.any(Object)
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe("error handling", () => {
    it("should throw error when API token is not set", async () => {
      delete process.env.PROMPTFOO_API_TOKEN;

      await expect(createRedteamConfig({})).rejects.toThrow(
        "PROMPTFOO_API_TOKEN not set"
      );
    });

    it("should handle API errors with error message", async () => {
      const mockError = new Error("API Error");
      axios.post.mockRejectedValueOnce(mockError);

      await expect(createRedteamConfig({ purpose: "test" })).rejects.toThrow(
        "API Error"
      );
    });

    it("should handle network errors", async () => {
      const mockError = new Error("Network Error");
      axios.get.mockRejectedValueOnce(mockError);

      await expect(getRedteamResults()).rejects.toThrow("Network Error");
    });
  });
});
